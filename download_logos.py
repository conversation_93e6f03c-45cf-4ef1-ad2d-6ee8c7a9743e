from google_images_download import google_images_download
import os

# Manual adjustments to client names with accented characters
clients = [
    "CITIC Europe Holdings a.s.",
    "Ceska posta logo",
    "Ceska sporitelna logo",
    "Ceska televize logo",
    "CEZ ICT Services logo",
    "FID Group logo",
    "HB Reavis Investments Slovakia logo",
    "Komerci banka logo",
    "Moneta Money Bank logo",
    "Nemocnice Ceske Budejovice logo",
    "Patria investicni spolecnost logo",
    "Penta SK logo",
    "ProLogis logo",
    "Property Management Solutions logo",
    "ROSSY service logo",
    "Rizeni letoveho provozu logo",
    "STRABAG Property and Facility Services logo",
    "T-Mobile logo",
    "TUV Nord Czech Republic logo",
    "UniCredit Bank logo",
    "VGP logo",
    "Vodafone logo"
]

# Set up the downloader
response = google_images_download.googleimagesdownload()

# Directory for saving logos
os.makedirs("logos", exist_ok=True)

# Fetch logos for each client
for client in clients:
    query = f"{client}"
    arguments = {
        "keywords": query,
        "limit": 1,
        "output_directory": "logos",
        "print_urls": True,
        "format": "jpg",
    }
    try:
        response.download(arguments)
    except Exception as e:
        print(f"Error downloading logo for {client}: {e}")