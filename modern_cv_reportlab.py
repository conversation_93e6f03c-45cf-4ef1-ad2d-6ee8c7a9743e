from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY
from datetime import datetime
import os

class ModernCVGenerator:
    def __init__(self):
        self.personal_info = {
            'name': 'Petr Prochazka',
            'title': 'Lead Analyst & Senior IT Systems Architect (Ing., Ph.D.)',
            'phone': '+420 731 503 275',
            'email': '<EMAIL>',
            'address': 'K Beranovu 1181/9, 184 00 Praha 8, Czech Republic',
            'linkedin': 'linkedin.com/in/petr-prochazka-197b2a14'
        }

        self.professional_summary = """
        Lead Analyst and Senior IT Systems Architect (Ing., Ph.D.) with 18+ years of extensive expertise
        in process analysis, system design, and enterprise solution delivery. Highly skilled professional
        specializing in comprehensive business process optimization, CRM implementations, and facility
        management systems for major Czech and international corporations. Proven track record of leading
        complex analytical projects and system integrations for telecommunications leaders (T-Mobile, Vodafone),
        financial institutions (Komercni Banka, Ceska sporitelna), and enterprise clients across multiple
        industries. Expert in translating business requirements into scalable technical architectures
        while ensuring operational excellence and strategic alignment.
        """

        self.key_achievements = [
            "Led enterprise system implementations for major Czech telecommunications providers (T-Mobile, Vodafone)",
            "Designed and delivered CRM and Facility Management solutions for financial sector (Komerční Banka)",
            "Successfully managed IT projects for aviation industry (ČSA) and logistics (ABF)",
            "Developed innovative data compression algorithms published in international conferences",
            "Awarded Dean's Prize for Excellence in Diploma Thesis (2008)"
        ]

        self.professional_experience = [
            {
                'company': 'Alstanet, s.r.o.',
                'position': 'Senior IT Analyst & Systems Architect',
                'period': 'May 2006 - Present',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Lead consultant for enterprise system analysis and design of Facility Management Systems and CRM solutions',
                    'Successfully delivered complex IT projects for major clients including T-Mobile, Vodafone, Komerční Banka, ČSA, and ABF',
                    'Designed scalable system architectures supporting thousands of concurrent users',
                    'Collaborated with cross-functional teams to ensure seamless system integration and deployment',
                    'Provided strategic IT consulting to optimize business processes and improve operational efficiency'
                ]
            },
            {
                'company': 'ComSTAR, s.r.o.',
                'position': 'CRM Systems Developer',
                'period': 'May 2005 - May 2006',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Developed comprehensive CRM system using PHP and SQL technologies',
                    'Implemented database optimization strategies improving system performance by 40%',
                    'Created user-friendly interfaces enhancing customer relationship management processes'
                ]
            }
        ]

        self.technical_skills = {
            'Programming Languages': ['Java', 'C/C++', 'PHP', 'Python', 'SQL', 'JavaScript'],
            'Databases': ['MySQL', 'PostgreSQL', 'Oracle', 'SQL Server'],
            'Frameworks & Tools': ['Spring', 'Hibernate', 'Laravel', 'Git', 'Jenkins'],
            'System Design': ['UML', 'Enterprise Architect', 'MS Visio', 'CASE Studio'],
            'Methodologies': ['Unified Process', 'Agile', 'GRASP', 'GoF Design Patterns'],
            'Operating Systems': ['Linux', 'Windows Server', 'Unix variants', 'Solaris']
        }

        self.education = [
            {
                'degree': 'Ph.D. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2009 - 2014',
                'focus': 'Natural Language Processing & Data Compression'
            },
            {
                'degree': 'M.S. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2006 - 2008',
                'focus': 'System Programming'
            },
            {
                'degree': 'B.S. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2003 - 2006',
                'focus': 'Computer Science'
            }
        ]

        self.certifications_awards = [
            'Dean\'s Prize for Excellent Diploma Thesis (2008)',
            'University Scholarship (2004-2008)',
            'Certificate of Achievement - Binghamton University, NY (2008)'
        ]

    def setup_styles(self):
        styles = getSampleStyleSheet()

        # Define custom colors
        primary_color = HexColor('#667eea')
        secondary_color = HexColor('#764ba2')
        text_color = HexColor('#333333')
        light_gray = HexColor('#666666')

        # Custom styles
        custom_styles = {
            'name': ParagraphStyle(
                'name',
                parent=styles['Heading1'],
                fontSize=28,
                textColor=white,
                alignment=TA_CENTER,
                spaceAfter=6,
                fontName='Helvetica-Bold'
            ),
            'title': ParagraphStyle(
                'title',
                parent=styles['Normal'],
                fontSize=16,
                textColor=white,
                alignment=TA_CENTER,
                spaceAfter=12,
                fontName='Helvetica'
            ),
            'contact': ParagraphStyle(
                'contact',
                parent=styles['Normal'],
                fontSize=10,
                textColor=white,
                alignment=TA_CENTER,
                fontName='Helvetica'
            ),
            'section_title': ParagraphStyle(
                'section_title',
                parent=styles['Heading2'],
                fontSize=14,
                textColor=primary_color,
                spaceAfter=12,
                spaceBefore=20,
                fontName='Helvetica-Bold'
            ),
            'body': ParagraphStyle(
                'body',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                alignment=TA_JUSTIFY,
                spaceAfter=6,
                fontName='Helvetica'
            ),
            'achievement': ParagraphStyle(
                'achievement',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                leftIndent=15,
                bulletIndent=10,
                spaceAfter=4,
                fontName='Helvetica'
            ),
            'job_title': ParagraphStyle(
                'job_title',
                parent=styles['Normal'],
                fontSize=12,
                textColor=text_color,
                fontName='Helvetica-Bold',
                spaceAfter=2
            ),
            'company': ParagraphStyle(
                'company',
                parent=styles['Normal'],
                fontSize=11,
                textColor=primary_color,
                fontName='Helvetica-Bold',
                spaceAfter=2
            ),
            'job_meta': ParagraphStyle(
                'job_meta',
                parent=styles['Normal'],
                fontSize=9,
                textColor=light_gray,
                fontName='Helvetica',
                spaceAfter=6
            ),
            'skill_category': ParagraphStyle(
                'skill_category',
                parent=styles['Normal'],
                fontSize=11,
                textColor=text_color,
                fontName='Helvetica-Bold',
                spaceAfter=4
            ),
            'skill_items': ParagraphStyle(
                'skill_items',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                fontName='Helvetica',
                spaceAfter=8
            ),
            'education_degree': ParagraphStyle(
                'education_degree',
                parent=styles['Normal'],
                fontSize=11,
                textColor=text_color,
                fontName='Helvetica-Bold',
                spaceAfter=2
            ),
            'education_institution': ParagraphStyle(
                'education_institution',
                parent=styles['Normal'],
                fontSize=10,
                textColor=primary_color,
                fontName='Helvetica-Bold',
                spaceAfter=2
            ),
            'education_meta': ParagraphStyle(
                'education_meta',
                parent=styles['Normal'],
                fontSize=9,
                textColor=light_gray,
                fontName='Helvetica',
                spaceAfter=8
            )
        }

        return custom_styles

    def create_header(self, styles):
        elements = []

        # Header background table
        header_data = [
            [Paragraph(self.personal_info['name'], styles['name'])],
            [Paragraph(self.personal_info['title'], styles['title'])],
            [Paragraph(f"📞 {self.personal_info['phone']} | ✉️ {self.personal_info['email']}", styles['contact'])],
            [Paragraph(f"📍 {self.personal_info['address']}", styles['contact'])],
            [Paragraph(f"💼 {self.personal_info['linkedin']}", styles['contact'])]
        ]

        header_table = Table(header_data, colWidths=[7.5*inch])
        header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), HexColor('#667eea')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 15),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('LEFTPADDING', (0, 0), (-1, -1), 20),
            ('RIGHTPADDING', (0, 0), (-1, -1), 20),
        ]))

        elements.append(header_table)
        elements.append(Spacer(1, 20))

        return elements

    def create_section(self, title, content, styles):
        elements = []

        # Section title with underline
        elements.append(Paragraph(title, styles['section_title']))
        elements.append(HRFlowable(width="100%", thickness=2, color=HexColor('#667eea')))
        elements.append(Spacer(1, 10))

        # Add content
        elements.extend(content)
        elements.append(Spacer(1, 15))

        return elements

    def create_professional_summary(self, styles):
        content = [Paragraph(self.professional_summary.strip(), styles['body'])]
        return self.create_section("Professional Summary", content, styles)

    def create_key_achievements(self, styles):
        content = []
        for achievement in self.key_achievements:
            content.append(Paragraph(f"▶ {achievement}", styles['achievement']))
        return self.create_section("Key Achievements", content, styles)

    def create_professional_experience(self, styles):
        content = []

        for exp in self.professional_experience:
            # Job title and company
            content.append(Paragraph(exp['position'], styles['job_title']))
            content.append(Paragraph(exp['company'], styles['company']))
            content.append(Paragraph(f"{exp['period']} | {exp['location']}", styles['job_meta']))

            # Achievements
            for achievement in exp['achievements']:
                content.append(Paragraph(f"• {achievement}", styles['achievement']))

            content.append(Spacer(1, 10))

        return self.create_section("Professional Experience", content, styles)

    def create_technical_skills(self, styles):
        content = []

        for category, skills in self.technical_skills.items():
            content.append(Paragraph(category, styles['skill_category']))
            skills_text = " • ".join(skills)
            content.append(Paragraph(skills_text, styles['skill_items']))

        return self.create_section("Technical Skills", content, styles)

    def create_education(self, styles):
        content = []

        for edu in self.education:
            content.append(Paragraph(edu['degree'], styles['education_degree']))
            content.append(Paragraph(edu['institution'], styles['education_institution']))
            content.append(Paragraph(f"{edu['period']} | Focus: {edu['focus']}", styles['education_meta']))

        return self.create_section("Education", content, styles)

    def create_certifications_awards(self, styles):
        content = []
        for award in self.certifications_awards:
            content.append(Paragraph(f"🏆 {award}", styles['achievement']))
        return self.create_section("Certifications & Awards", content, styles)

    def generate_cv(self, output_filename="petr_prochazka_modern_cv_updated.pdf"):
        try:
            # Create document
            doc = SimpleDocTemplate(
                output_filename,
                pagesize=A4,
                rightMargin=25*mm,
                leftMargin=25*mm,
                topMargin=20*mm,
                bottomMargin=20*mm
            )

            # Setup styles
            styles = self.setup_styles()

            # Build document content
            story = []

            # Header
            story.extend(self.create_header(styles))

            # Professional Summary
            story.extend(self.create_professional_summary(styles))

            # Key Achievements
            story.extend(self.create_key_achievements(styles))

            # Professional Experience
            story.extend(self.create_professional_experience(styles))

            # Technical Skills
            story.extend(self.create_technical_skills(styles))

            # Education
            story.extend(self.create_education(styles))

            # Certifications & Awards
            story.extend(self.create_certifications_awards(styles))

            # Build PDF
            doc.build(story)

            print(f"✅ Modern CV successfully generated: {output_filename}")
            return True

        except Exception as e:
            print(f"❌ Error generating PDF: {str(e)}")
            return False

if __name__ == "__main__":
    generator = ModernCVGenerator()
    generator.generate_cv()
