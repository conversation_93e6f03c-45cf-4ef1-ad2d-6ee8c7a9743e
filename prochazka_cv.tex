%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%% Simple LaTeX CV Template %%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% NOTE: If you find that it says                                     %%
%%                                                                    %%
%%                           1 of ??                                  %%
%%                                                                    %%
%% at the bottom of your first page, this means that the AUX file     %%
%% was not available when you ran LaTeX on this source. Simply RERUN  %% 
%% LaTeX to get the ``??'' replaced with the number of the last page  %% 
%% of the document. The AUX file will be generated on the first run   %%
%% of LaTeX and used on the second run to fill in all of the          %%
%% references.                                                        %%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%% Document Setup %%%%%%%%%%%%%%%%%%%%%%%%%%%%

% Don't like 10pt? Try 11pt or 12pt
\documentclass[10pt]{article}

% This is a helpful package that puts math inside length specifications
\usepackage{calc}

% Layout: Puts the section titles on left side of page
\reversemarginpar

%
%         PAPER SIZE, PAGE NUMBER, AND DOCUMENT LAYOUT NOTES:
%
% The next \usepackage line changes the layout for CV style section
% headings as marginal notes. It also sets up the paper size as either
% letter or A4. By default, letter was used. If A4 paper is desired,
% comment out the letterpaper lines and uncomment the a4paper lines.
%
% As you can see, the margin widths and section title widths can be
% easily adjusted.
%
% ALSO: Notice that the includefoot option can be commented OUT in order
% to put the PAGE NUMBER *IN* the bottom margin. This will make the
% effective text area larger.
%
% IF YOU WISH TO REMOVE THE ``of LASTPAGE'' next to each page number,
% see the note about the +LP and -LP lines below. Comment out the +LP
% and uncomment the -LP.
%
% IF YOU WISH TO REMOVE PAGE NUMBERS, be sure that the includefoot line
% is uncommented and ALSO uncomment the \pagestyle{empty} a few lines
% below.
%

%% Use these lines for letter-sized paper
\usepackage[paper=letterpaper,
            %includefoot, % Uncomment to put page number above margin
            marginparwidth=1.2in,     % Length of section titles
            marginparsep=.05in,       % Space between titles and text
            margin=1in,               % 1 inch margins
            includemp]{geometry}

%% Use these lines for A4-sized paper
%\usepackage[paper=a4paper,
%            %includefoot, % Uncomment to put page number above margin
%            marginparwidth=30.5mm,    % Length of section titles
%            marginparsep=1.5mm,       % Space between titles and text
%            margin=25mm,              % 25mm margins
%            includemp]{geometry}

%% More layout: Get rid of indenting throughout entire document
\setlength{\parindent}{0in}

%% This gives us fun enumeration environments. compactenum will be nice.
\usepackage{paralist}

%% Reference the last page in the page number
%
% NOTE: comment the +LP line and uncomment the -LP line to have page
%       numbers without the ``of ##'' last page reference)
%
% NOTE: uncomment the \pagestyle{empty} line to get rid of all page
%       numbers (make sure includefoot is commented out above)
%
\usepackage{fancyhdr,lastpage}
\pagestyle{fancy}
%\pagestyle{empty}      % Uncomment this to get rid of page numbers
\fancyhf{}\renewcommand{\headrulewidth}{0pt}
\fancyfootoffset{\marginparsep+\marginparwidth}
\newlength{\footpageshift}
\setlength{\footpageshift}
          {0.5\textwidth+0.5\marginparsep+0.5\marginparwidth-2in}
\lfoot{\hspace{\footpageshift}%
       \parbox{4in}{\, \hfill %
                    \arabic{page} of \protect\pageref*{LastPage} % +LP
%                    \arabic{page}                               % -LP
                    \hfill \,}}

% Finally, give us PDF bookmarks
\usepackage{color,hyperref}
\definecolor{darkblue}{rgb}{0.0,0.0,0.3}
\hypersetup{colorlinks,breaklinks,
            linkcolor=darkblue,urlcolor=darkblue,
            anchorcolor=darkblue,citecolor=darkblue}

%%%%%%%%%%%%%%%%%%%%%%%% End Document Setup %%%%%%%%%%%%%%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%%%%%%%%%%%%% Helper Commands %%%%%%%%%%%%%%%%%%%%%%%%%%%%

% The title (name) with a horizontal rule under it
%
% Usage: \makeheading{name}
%
% Place at top of document. It should be the first thing.
\newcommand{\makeheading}[1]%
        {\hspace*{-\marginparsep minus \marginparwidth}%
         \begin{minipage}[t]{\textwidth+\marginparwidth+\marginparsep}%
                {\large \bfseries #1}\\[-0.15\baselineskip]%
                 \rule{\columnwidth}{1pt}%
         \end{minipage}}

% The section headings
%
% Usage: \section{section name}
%
% Follow this section IMMEDIATELY with the first line of the section
% text. Do not put whitespace in between. That is, do this:
%
%       \section{My Information}
%       Here is my information.
%
% and NOT this:
%
%       \section{My Information}
%
%       Here is my information.
%
% Otherwise the top of the section header will not line up with the top
% of the section. Of course, using a single comment character (%) on
% empty lines allows for the function of the first example with the
% readability of the second example.
\renewcommand{\section}[2]%
        {\pagebreak[2]\vspace{1.3\baselineskip}%
         \phantomsection\addcontentsline{toc}{section}{#1}%
         \hspace{0in}%
         \marginpar{
         \raggedright \scshape #1}#2}

% An itemize-style list with lots of space between items
\newenvironment{outerlist}[1][\enskip\textbullet]%
        {\begin{enumerate}[#1]}{\end{enumerate}%
         \vspace{-.6\baselineskip}}

% An environment IDENTICAL to outerlist that has better pre-list spacing
% when used as the first thing in a \section
\newenvironment{lonelist}[1][\enskip\textbullet]%
        {\vspace{-\baselineskip}\begin{list}{#1}{%
        \setlength{\partopsep}{0pt}%
        \setlength{\topsep}{0pt}}}
        {\end{list}\vspace{-.6\baselineskip}}

% An itemize-style list with little space between items
\newenvironment{innerlist}[1][\enskip\textbullet]%
        {\begin{compactenum}[#1]}{\end{compactenum}}

% To add some paragraph space between lines.
% This also tells LaTeX to preferably break a page on one of these gaps
% if there is a needed pagebreak nearby.
\newcommand{\blankline}{\quad\pagebreak[2]}

%%%%%%%%%%%%%%%%%%%%%%%% End Helper Commands %%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%% Begin CV Document %%%%%%%%%%%%%%%%%%%%%%%%%%%%

\begin{document}
\makeheading{Petr Proch\'{a}zka}

\section{Contact Information}
%
% NOTE: Mind where the & separators and \\ breaks are in the following
%       table.
%
% ALSO: \rcollength is the width of the right column of the table 
%       (adjust it to your liking; default is 1.85in).
%
\newlength{\rcollength}\setlength{\rcollength}{1.85in}%
%
\begin{tabular}[t]{@{}p{\textwidth-\rcollength}p{\rcollength}}
\textit{Voice:} ~~(+420) 775 687 323 \\
\textit{E-mail:} ~\href{mailto:<EMAIL>}{<EMAIL>}\\
\textit{Address:} N\'{a}b\v{r}e\v{z}n\'{i} 52\\
~~~~~~~~~~~~28561 \v{Z}leby\\
~~~~~~~~~~~~Czech Republic\\
\end{tabular}

\section{Citizenship}
%
Czech Republic

\section{Research Interests}
%
Stringology, Pattern Matching, Data Compression, Database Systems.

\section{Education}
%
\href{http://www.fel.cvut.cz/}{\textbf{Czech Technical University in Prague}}, 
Prague, Czech Republic
\begin{outerlist}

\item[] Ph.D., 
        \href{http://cs.felk.cvut.cz}
             {Computer Science and Engineering} 
        (2009 - now)
        \begin{innerlist}
        \item Thesis Topic: Natural Language Text Compression
        \item Advisor: 
              \href{http://cs.felk.cvut.cz/~holub}
                   {Doc. Ing. Jan Holub, Ph.D.}
        \item Area of Study: Natural Language Processing, Data Compression, Stringology.
        \end{innerlist}

\item[] M.S., 
        \href{http://cs.felk.cvut.cz}
             {Computer Science and Engineering} 
        (graduation date: June 2008)
        \begin{innerlist}
        \item Thesis Topic: Word-based Statistical Data Compression Methods
        \item Advisor: 
              \href{http://cs.felk.cvut.cz/~holub}
                   {Ing. Jan Holub, Ph.D.}
        \item Area of Study: System Programming
        \end{innerlist}

\item[] B.S., 
        \href{http://cs.felk.cvut.cz}
             {Computer Science and Engineering} 
        (graduation date: September 2006)
        \begin{innerlist}
        \item Thesis Topic: The SyncML technology as a synchronisation tool for mobile devices
        \item Advisor: 
              \href{http://cs.felk.cvut.cz/~danecek}
                   {Ing. Ji\v{r}\'{i} Dan\v{e}\v{c}ek}
        \item Area of Study: Computer Science
        \end{innerlist}

\end{outerlist}

\section{Publication} 
%
\begin{innerlist}
\item Proch\'{a}zka P., Holub J.: New Word-based Adaptive Dense Compressors. Proceedings of the 20th Workshop on Combinatorial Algorithms (IWOCA09), Hradec nad Moravici, June 2009.
\item Proch\'{a}zka P., Jaro\v{s} J.: On Implementation of Word-Based Compression Methods. Proceedings of the 4th Doctoral Workshop on Mathematical and Engineering Methods in Computer Science (MEMICS 2009), Brno, November 2008.
\end{innerlist}


\section{Teaching Experience} 
%
\begin{innerlist}
\item Programming in Java.
\item Database Systems.
\end{innerlist}


\section{Awards} 
%
\href{http://www.fel.cvut.cz}{\textbf{Czech Technical University in Prague}}
\begin{innerlist}
\item Dean's Prize for Excellent Diploma Thesis, 2008
\item University Scholarship, 2004-2008
\end{innerlist}

\blankline

\href{http://www.binghamton.edu}{\textbf{Binghamton University, State University of New York}}
\begin{innerlist}
\item Certificate of Achievement, 2008
\end{innerlist}


\section{Academic Experience}
\href{http://www.fel.cvut.cz}{\textbf{Czech Technical University in Prague}}
\begin{outerlist}

\item[] \textit{PhD student}%
        \hfill \textbf{March 2009 to now}

\item[] \textit{Graduate Student}%
        \hfill \textbf{September 2006 to June 2008}

\item[] \textit{Undergraduate Student}%
        \hfill \textbf{September 2003 to September 2006}
\end{outerlist}

\blankline

\href{http://www.binghamton.edu}{\textbf{Binghamton University, State University of New York}}
\begin{outerlist}
\item[] \textit{Graduate Student}%
        \hfill \textbf{January 2008 to May 2008}
\item[] \textit{Research Assistant}%
        \hfill \textbf{January 2008 to May 2008}
\end{outerlist}


\section{Professional Experience}
%
\href{http://www.alstanet.cz/}{\textbf{Alstanet, s.r.o.}}, 
Praha, Czech Republic
\begin{outerlist}

\item[] \textit{Senior Analyst}%
        \hfill \textbf{May 2006 to present}
\begin{innerlist}
\item Consultation, analysis and design of Facility Management System, CRM and other IS.
\item Cooperated on projects: T-mobile, Vodafone, KB, \v{C}SA, ABF etc.
\end{innerlist}
\end{outerlist}

\blankline

\href{http://www.comstar.cz/}{\textbf{ComSTAR, s.r.o.}}, 
Praha, Czech Republic
\begin{outerlist}

\item[] \textit{ComSTARNet Developer}%
        \hfill \textbf{May 2005 to May 2006}
\begin{innerlist}
\item Developing of CRM system using PHP and SQL.
\end{innerlist}
\end{outerlist}

\section{Technical\\ Skills} 
%
Extensive hardware and software experience in networking and
        information technology.

\blankline

Analysis and Design Techniques: Unified Process, UML, Use Cases, GRASP and GoF Design Patterns.

\blankline

Analysis and Design Tools: Enterprise Architect, MS Visio, CASE Studio.

\blankline

Programming: C, C++, Java, Pascal, PHP, Smalltalk, UNIX shell scripting, SQL and others.

\blankline

Applications: \TeX{}, \LaTeX{}, B\textsc{ib}\TeX{}, Microsoft Office,
        and other common productivity packages for Windows, and
        Linux platforms.

\blankline

Operating Systems: Microsoft Windows Vista/XP/2000, Linux, Solaris, and other UNIX variants.

\end{document}

%%%%%%%%%%%%%%%%%%%%%%%%%% End CV Document %%%%%%%%%%%%%%%%%%%%%%%%%%%%%
