from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY
from datetime import datetime
import os

class FinalCVGenerator:
    def __init__(self):
        self.personal_info = {
            'name': 'Petr Prochazka',
            'title': 'Lead Analyst & Senior IT Systems Architect (Ing., Ph.D.)',
            'phone': '+420 731 503 275',
            'email': '<EMAIL>',
            'address': 'K Beranovu 1181/9, 184 00 Praha 8, Czech Republic',
            'linkedin': 'https://www.linkedin.com/in/petr-proch%C3%A1zka-197b2a14/'
        }

        self.professional_summary = """
        Lead Analyst and Senior IT Systems Architect (Ing., Ph.D.) with 18+ years of extensive expertise
        in process analysis, system design, and enterprise solution delivery. Highly skilled professional
        specializing in comprehensive business process optimization, CRM implementations, and facility
        management systems for major Czech and international corporations. Proven track record of leading
        complex analytical projects and system integrations for telecommunications leaders (T-Mobile, Vodafone),
        financial institutions (Komercni Banka, Ceska sporitelna), and enterprise clients across multiple
        industries. Expert in translating business requirements into scalable technical architectures
        while ensuring operational excellence and strategic alignment.
        """



        # Organized clients by industry for better presentation
        self.key_clients = {
            'Financial Services': [
                'Komercni banka, a.s.',
                'Ceska sporitelna, a.s.',
                'UniCredit Bank Czech Republic and Slovakia, a. s.',
                'Patria investicni spolecnost, a.s.'
            ],
            'Telecommunications': [
                'T-Mobile CR, a.s.',
                'Vodafone'
            ],
            'Energy & Utilities': [
                'CEZ ICT Services, a. s.',
                'Ceska posta, s. p.'
            ],
            'Real Estate & Construction': [
                'AB Facility a.s.',
                'HB Reavis Investments Slovakia s. r. o.',
                'Property Management Solutions s.r.o.',
                'STRABAG Property and Facility Services a.s. (CZ)',
                'VGP - industrialni stavby, s.r.o.'
            ],
            'Media & Healthcare': [
                'Ceska televize',
                'Nemocnice Ceske Budejovice, a.s.'
            ],
            'Other Enterprise': [
                'Alstanet, s.r.o.',
                'AWIGO s. r. o.',
                'CITIC Europe Holdings a.s.',
                'FID Group',
                'Penta SK',
                'ROSSY service a.s.',
                'Rizeni letoveho provozu',
                'TUV Nord Czech Republic'
            ]
        }

        self.professional_experience = [
            {
                'company': 'Alstanet, s.r.o.',
                'position': 'Lead Analyst & Senior IT Systems Architect',
                'period': 'May 2006 - Present (18+ years)',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Lead comprehensive business process analysis and optimization for enterprise clients across multiple industries',
                    'Principal architect for large-scale CRM and Facility Management solutions serving millions of users',
                    'Successfully delivered 50+ complex analytical and IT projects with combined value exceeding €25M',
                    'Managed cross-functional teams of 15+ analysts, developers, and project managers',
                    'Established analytical methodologies and system architecture best practices resulting in 40% faster project delivery',
                    'Key client relationships: T-Mobile, Vodafone, Komercni Banka, Ceska sporitelna, CSA, ABF Freight Systems'
                ]
            },
            {
                'company': 'ComSTAR, s.r.o.',
                'position': 'Senior CRM Developer & Systems Analyst',
                'period': 'May 2005 - May 2006',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Developed enterprise CRM platform using advanced PHP and SQL optimization techniques',
                    'Implemented performance improvements resulting in 40% faster query execution',
                    'Created intuitive user interfaces that reduced training time by 50%'
                ]
            }
        ]

        self.technical_expertise = {
            'Process Analysis & Optimization': ['Business Process Analysis', 'Process Modeling', 'Workflow Optimization', 'Performance Analysis'],
            'Enterprise Architecture': ['System Design', 'Microservices', 'API Integration', 'Cloud Architecture'],
            'Programming & Development': ['Java', 'C/C++', 'PHP', 'Python', 'JavaScript', 'SQL'],
            'Database Technologies': ['Oracle', 'MySQL', 'PostgreSQL', 'SQL Server'],
            'Project Management': ['Agile/Scrum', 'DevOps', 'Git', 'Jenkins', 'JIRA'],
            'Business Analysis': ['UML', 'BPMN', 'Enterprise Architect', 'Requirements Engineering']
        }

        self.education = [
            {
                'degree': 'Ph.D. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2009 - 2014',
                'focus': 'Advanced Algorithms & Data Compression'
            },
            {
                'degree': 'M.S. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2006 - 2008',
                'focus': 'System Programming & Architecture'
            }
        ]

        self.certifications_awards = [
            'Dean\'s Prize for Outstanding Academic Excellence (2008)',
            'University Merit Scholarship (2004-2008)',
            'International Research Achievement Award - Binghamton University, NY (2008)',
            'Published researcher in international computer science conferences'
        ]

    def setup_styles(self):
        styles = getSampleStyleSheet()

        # Define modern color palette
        primary_color = HexColor('#2C3E50')  # Dark blue-gray
        accent_color = HexColor('#3498DB')   # Bright blue
        success_color = HexColor('#27AE60')  # Green
        text_color = HexColor('#2C3E50')
        light_gray = HexColor('#7F8C8D')

        custom_styles = {
            'name': ParagraphStyle(
                'name',
                parent=styles['Heading1'],
                fontSize=32,
                textColor=white,
                alignment=TA_CENTER,
                spaceAfter=8,
                fontName='Helvetica-Bold'
            ),
            'title': ParagraphStyle(
                'title',
                parent=styles['Normal'],
                fontSize=14,
                textColor=white,
                alignment=TA_CENTER,
                spaceAfter=15,
                fontName='Helvetica'
            ),
            'contact': ParagraphStyle(
                'contact',
                parent=styles['Normal'],
                fontSize=10,
                textColor=white,
                alignment=TA_CENTER,
                fontName='Helvetica'
            ),
            'section_title': ParagraphStyle(
                'section_title',
                parent=styles['Heading2'],
                fontSize=16,
                textColor=primary_color,
                spaceAfter=15,
                spaceBefore=25,
                fontName='Helvetica-Bold'
            ),
            'body': ParagraphStyle(
                'body',
                parent=styles['Normal'],
                fontSize=11,
                textColor=text_color,
                alignment=TA_JUSTIFY,
                spaceAfter=8,
                fontName='Helvetica',
                leading=14
            ),
            'project_title': ParagraphStyle(
                'project_title',
                parent=styles['Normal'],
                fontSize=12,
                textColor=accent_color,
                fontName='Helvetica-Bold',
                spaceAfter=4
            ),
            'project_desc': ParagraphStyle(
                'project_desc',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                fontName='Helvetica',
                spaceAfter=3
            ),
            'project_impact': ParagraphStyle(
                'project_impact',
                parent=styles['Normal'],
                fontSize=10,
                textColor=success_color,
                fontName='Helvetica-Bold',
                spaceAfter=10
            ),
            'industry_title': ParagraphStyle(
                'industry_title',
                parent=styles['Normal'],
                fontSize=11,
                textColor=primary_color,
                fontName='Helvetica-Bold',
                spaceAfter=4,
                spaceBefore=8
            ),
            'client_name': ParagraphStyle(
                'client_name',
                parent=styles['Normal'],
                fontSize=9,
                textColor=text_color,
                fontName='Helvetica',
                spaceAfter=2
            ),
            'client_name_enhanced': ParagraphStyle(
                'client_name_enhanced',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                fontName='Helvetica',
                spaceAfter=4,
                leftIndent=20,
                bulletIndent=15
            ),
            'achievement': ParagraphStyle(
                'achievement',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                leftIndent=15,
                bulletIndent=10,
                spaceAfter=5,
                fontName='Helvetica'
            ),
            'job_title': ParagraphStyle(
                'job_title',
                parent=styles['Normal'],
                fontSize=13,
                textColor=text_color,
                fontName='Helvetica-Bold',
                spaceAfter=3
            ),
            'company': ParagraphStyle(
                'company',
                parent=styles['Normal'],
                fontSize=12,
                textColor=accent_color,
                fontName='Helvetica-Bold',
                spaceAfter=3
            ),
            'job_meta': ParagraphStyle(
                'job_meta',
                parent=styles['Normal'],
                fontSize=10,
                textColor=light_gray,
                fontName='Helvetica',
                spaceAfter=8
            ),
            'skill_category': ParagraphStyle(
                'skill_category',
                parent=styles['Normal'],
                fontSize=11,
                textColor=primary_color,
                fontName='Helvetica-Bold',
                spaceAfter=4
            ),
            'skill_items': ParagraphStyle(
                'skill_items',
                parent=styles['Normal'],
                fontSize=10,
                textColor=text_color,
                fontName='Helvetica',
                spaceAfter=8
            )
        }

        return custom_styles

    def create_header(self, styles):
        elements = []

        # Header with gradient-like effect
        header_data = [
            [Paragraph(self.personal_info['name'], styles['name'])],
            [Paragraph(self.personal_info['title'], styles['title'])],
            [Spacer(1, 5)],
            [Paragraph(f"📞 {self.personal_info['phone']} | ✉️ {self.personal_info['email']}", styles['contact'])],
            [Paragraph(f"📍 {self.personal_info['address']}", styles['contact'])],
            [Paragraph(f"💼 {self.personal_info['linkedin']}", styles['contact'])]
        ]

        header_table = Table(header_data, colWidths=[7.5*inch])
        header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), HexColor('#2C3E50')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('TOPPADDING', (0, 0), (-1, -1), 20),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
            ('LEFTPADDING', (0, 0), (-1, -1), 25),
            ('RIGHTPADDING', (0, 0), (-1, -1), 25),
        ]))

        elements.append(header_table)
        elements.append(Spacer(1, 25))

        return elements

    def create_section(self, title, content, styles):
        elements = []

        # Section title with modern underline
        elements.append(Paragraph(title, styles['section_title']))
        elements.append(HRFlowable(width="100%", thickness=3, color=HexColor('#3498DB')))
        elements.append(Spacer(1, 12))

        # Add content
        elements.extend(content)
        elements.append(Spacer(1, 20))

        return elements

    def create_professional_summary(self, styles):
        content = [Paragraph(self.professional_summary.strip(), styles['body'])]
        return self.create_section("Executive Summary", content, styles)

    def create_key_projects(self, styles):
        content = []

        # Add clients section organized by industry with better formatting
        content.append(Paragraph("🏢 Key Enterprise Clients by Industry", styles['project_title']))
        content.append(Spacer(1, 8))

        # Industry icons mapping
        industry_icons = {
            'Financial Services': '🏦',
            'Telecommunications': '📱',
            'Energy & Utilities': '⚡',
            'Real Estate & Construction': '🏗️',
            'Media & Healthcare': '🏥',
            'Other Enterprise': '🏢'
        }

        # Create a more structured layout with better spacing
        for industry, clients in self.key_clients.items():
            icon = industry_icons.get(industry, '🏢')
            content.append(Paragraph(f"{icon} <b>{industry}</b>", styles['industry_title']))
            content.append(Spacer(1, 3))

            # Create client list with bullet points and better spacing
            for client in clients:
                # Add company logo placeholder and name
                client_text = f"▶ <b>{client}</b>"
                content.append(Paragraph(client_text, styles['client_name_enhanced']))

            content.append(Spacer(1, 10))

        return self.create_section("Key Enterprise Clients", content, styles)

    def create_professional_experience(self, styles):
        content = []

        for exp in self.professional_experience:
            # Job title and company
            content.append(Paragraph(exp['position'], styles['job_title']))
            content.append(Paragraph(exp['company'], styles['company']))
            content.append(Paragraph(f"{exp['period']} | {exp['location']}", styles['job_meta']))

            # Achievements
            for achievement in exp['achievements']:
                content.append(Paragraph(f"▶ {achievement}", styles['achievement']))

            content.append(Spacer(1, 15))

        return self.create_section("Professional Experience", content, styles)

    def create_technical_expertise(self, styles):
        content = []

        for category, skills in self.technical_expertise.items():
            content.append(Paragraph(category, styles['skill_category']))
            skills_text = " • ".join(skills)
            content.append(Paragraph(skills_text, styles['skill_items']))

        return self.create_section("Technical Expertise", content, styles)

    def create_education(self, styles):
        content = []

        for edu in self.education:
            content.append(Paragraph(f"{edu['degree']}", styles['job_title']))
            content.append(Paragraph(f"{edu['institution']}", styles['company']))
            content.append(Paragraph(f"{edu['period']} | {edu['focus']}", styles['job_meta']))

        return self.create_section("Education", content, styles)

    def create_certifications_awards(self, styles):
        content = []
        for award in self.certifications_awards:
            content.append(Paragraph(f"🏆 {award}", styles['achievement']))
        return self.create_section("Awards & Recognition", content, styles)

    def generate_cv(self, output_filename="petr_prochazka_final_cv.pdf"):
        try:
            # Create document
            doc = SimpleDocTemplate(
                output_filename,
                pagesize=A4,
                rightMargin=20*mm,
                leftMargin=20*mm,
                topMargin=15*mm,
                bottomMargin=15*mm
            )

            # Setup styles
            styles = self.setup_styles()

            # Build document content
            story = []

            # Header
            story.extend(self.create_header(styles))

            # Professional Summary
            story.extend(self.create_professional_summary(styles))

            # Key Enterprise Clients organized by industry
            story.extend(self.create_key_projects(styles))

            # Professional Experience
            story.extend(self.create_professional_experience(styles))

            # Technical Expertise
            story.extend(self.create_technical_expertise(styles))

            # Education (shortened)
            story.extend(self.create_education(styles))

            # Awards & Recognition
            story.extend(self.create_certifications_awards(styles))

            # Build PDF
            doc.build(story)

            print(f"✅ Final CV successfully generated: {output_filename}")
            return True

        except Exception as e:
            print(f"❌ Error generating PDF: {str(e)}")
            return False

if __name__ == "__main__":
    generator = FinalCVGenerator()
    generator.generate_cv()
