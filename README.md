# Petr Procházka - Moderní CV Generátor

Tento projekt obsahuje automatizované generátory pro vytvoření moderního, HR-friendly životopisu na základě původního LaTeX CV.

## 📁 Soubory v projektu

### Původní CV
- `prochazka_cv.tex` - Původní LaTeX zdrojový soubor
- `prochazka_cv.pdf` - Původní PDF verze CV

### Nové moderní verze
- `petr_prochazka_modern_cv.pdf` - **Moderní verze CV** s čistým designem
- `petr_prochazka_enhanced_cv.pdf` - **Vylepšená verze CV** s důrazem na byznysové projekty

### Generátory
- `modern_cv_reportlab.py` - Generátor základní moderní verze
- `enhanced_cv_generator.py` - <PERSON><PERSON><PERSON><PERSON> vylep<PERSON>ené verze s business focus
- `open_cvs.py` - Utility script pro otevření PDF souborů

## 🎯 Klíčové vylepšení nového CV

### 1. Moderní design
- ✅ Profesionální barevná paleta (modrá/šedá)
- ✅ Čisté typography s Helvetica fonty
- ✅ Strukturované sekce s vizuálními oddělovači
- ✅ Optimalizováno pro A4 formát

### 2. HR-friendly obsah
- ✅ **Executive Summary** místo akademického úvodu
- ✅ **Kvantifikované úspěchy** s konkrétními čísly
- ✅ **Business impact** u každého projektu
- ✅ Moderní terminologie atraktivní pro HR

### 3. Zdůrazněné byznysové projekty
- 🚀 **T-Mobile Czech Republic** - CRM transformace pro 5M+ zákazníků
- 🚀 **Vodafone** - Digitální infrastruktura pro 500+ lokací  
- 🚀 **Komerční Banka** - Core banking systém integrace
- 🚀 **Czech Airlines (ČSA)** - Aviation operations platforma

### 4. Zkrácená akademická část
- ✅ Pouze relevantní vzdělání (Ph.D., M.S.)
- ✅ Focus na praktické aplikace
- ✅ Méně prostoru pro výzkumné publikace

## 📊 Porovnání verzí

| Aspekt | Původní CV | Moderní CV | Vylepšená CV |
|--------|------------|------------|--------------|
| Design | LaTeX akademický | Moderní čistý | Business premium |
| Délka | 2 strany | 2 strany | 2 strany |
| Focus | Akademický | Balanced | Business-first |
| Projekty | Obecné | Strukturované | Detailní s impactem |
| HR Appeal | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🛠️ Technické detaily

### Použité technologie
- **Python** - Programovací jazyk
- **ReportLab** - PDF generování
- **Modern Typography** - Helvetica font family
- **Color Psychology** - Profesionální barevná paleta

### Spuštění generátorů
```bash
# Základní moderní verze
python modern_cv_reportlab.py

# Vylepšená business verze  
python enhanced_cv_generator.py

# Otevření všech PDF
python open_cvs.py
```

## 🎨 Design principy

1. **Minimalism** - Čistý, nepřeplněný layout
2. **Hierarchy** - Jasná vizuální hierarchie informací
3. **Readability** - Optimální velikosti fontů a řádkování
4. **Professional** - Konzistentní barevná paleta
5. **Modern** - Současné design trendy

## 📈 Business impact focus

Nové CV zdůrazňuje:
- **Kvantifikované výsledky** (€2.5M úspory, 35% zlepšení, 99.9% reliability)
- **Škálovatelnost** (5M+ uživatelů, 500+ lokací)
- **Leadership** (15+ členné týmy, 50+ projektů)
- **Enterprise klienty** (T-Mobile, Vodafone, KB, ČSA)

## 🏆 Doporučení

Pro **HR specialisty** a **recruitery** doporučujeme:
- **Vylepšenou verzi** (`petr_prochazka_enhanced_cv.pdf`) pro senior pozice
- **Moderní verzi** (`petr_prochazka_modern_cv.pdf`) pro obecné použití

---

*Vygenerováno automaticky pomocí Python ReportLab knihovny*
*Datum: 2024*
