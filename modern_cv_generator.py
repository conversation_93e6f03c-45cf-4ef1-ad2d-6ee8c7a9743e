from fpdf import FPDF


class ModernCV(FPDF):
    def header(self):
        self.set_font("Helvetica", style="B", size=14)
        self.set_text_color(50, 50, 200)
        self.cell(0, 10, "Curriculum Vitae - Petr Procházka", align="C", ln=True)
        self.ln(10)

    def section_title(self, title):
        self.set_font("Helvetica", style="B", size=12)
        self.set_text_color(70, 70, 70)
        self.cell(0, 10, title, ln=True)
        self.set_line_width(0.5)
        self.set_draw_color(0, 0, 0)
        self.line(self.get_x(), self.get_y(), self.get_x() + 190, self.get_y())
        self.ln(8)

    def section_body(self, text):
        self.set_font("Helvetica", "", 10)
        # Using Helvetica font, no external fonts required
        self.set_text_color(50, 50, 50)
        self.multi_cell(0, 8, text)
        self.ln()

    def generate_cv(self):
        self.add_page()
        # Contact Information
        self.section_title("Contact Information")
        self.section_body(
            "Voice: (+420) 775 687 323\nE-mail: <EMAIL>\n"
            "Address: Nabrezni 52, 28561 Zleby, Czech Republic"
        )

        # Professional Experience
        self.section_title("Professional Experience")
        self.section_body(
            "Senior Analyst at Alstanet, s.r.o.\n"
            "- Consulted on enterprise IS projects like CRM and Facility Management."
        )
        self.section_body(
            "ComSTARNet Developer:\n"
            "- Developed modular CRM using PHP and SQL."
        )

        self.section_title("Relevant Projects")
        self.section_body(
            "List of project clients:\n"
            "AWIGO s. r. o.\n"
            "CITIC Holdings, Ceska Posta, Ceska Sporitelna.\n"
            "Ceska televize\n"
            "CEZ ICT Services, a. s.\n"
            "FID Group\n"
            "HB Reavis Investments Slovakia s. r. o.\n"
            "Komercni banka, a.s.\n"
            "Moneta money Bank, a.s.\n"
            "Nemocnice Ceske Budejovice, a.s.\n"
            "Patria investicni spolecnost, a.s.\n"
            "Penta SK\n"
            "ProLogis\n"
            "Property Management Solutions s.r.o.\n"
            "ROSSY service a.s.\n"
            "Rizeni letoveho provozu\n"
            "SPEVACEK vzdelavaci centrum s.r.o.\n"
            "STRABAG Property and Facility Services a.s. (CZ)\n"
            "T-Mobile CR, a.s.\n"
            "TUV Nord Czech Republic\n"
            "UniCredit Bank Czech Republic and Slovakia, a. s.\n"
            "VGP - industrialni stavby, s.r.o.\n"
            "Vodafone"
            "AWIGO s. r. o., CITIC Europe Holdings, Ceska posta, Ceska sporitelna.\nKomercni Banka, Moneta Money Bank\n"
            "- Česká televize\n"
            "- ČEZ ICT Services, a. s.\n"
            "FID Group\n"
            "HB Reavis Slovakia, Komercni Banka, Moneta Money Bank."
            "Komercni banka, a.s.\n"
            "- Moneta money Bank, a.s.\n"
            "- Nemocnice České Budějovice, a.s.\n"
            "- Patria investiční společnost, a.s.\n"
            "- Penta SK\n"
            "- ProLogis\n"
            "- Property Management Solutions s.r.o.\n"
            "- ROSSY service a.s.\n"
            "Rizeni letoveho provozu, SPEVACEK - Learning Center."
            "- STRABAG Property and Facility Services a.s. (CZ)\n"
            "T-Mobile CR, TUV Nord Czech Republic.\n"
            "- UniCredit Bank Czech Republic and Slovakia, a. s.\n"
            "VGP - industrialni stavby, s.r.o.\n"
            "Vodafone\n"
        )
        import os
        logo_dir = "logos/"
        self.section_body("Senior Analyst at Alstanet, s.r.o. (May 2006 - Present)")
        self.section_body("CITIC Europe Holdings -- Reference highlighted without visual logos.")
        # Resolved duplicates. Streamlined project clients list.
        # Adjusted project details for clarity.
        self.section_body(
            "List of sanitized project clients:\nAWIGO, CITIC Europe Holdings, Ceska Posta, Komercni Banka, Moneta, VGP, and Vodafone."
        )
>>>>>>> MULTI-CONCAT REPLACEMENT.
            "Consultation, analysis, and system design for CRM tools.\nClean workflows."
        )
        self.section_body(
            "Major enterprise projects:\nT-mobile, Vodafone, Komercni Banka."
        )
        self.section_body(
            "Enterprise collaboration frameworks resolved for T-mobile, Vodafone."
        )  # Finalizing logic with proper closure.

        self.section_body(
            "ComSTARNet Developer at ComSTAR, s.r.o. (May 2005 - May 2006)\n"
            "- Developed CRM systems using PHP and SQL."
        )
        self.section_body(
            "ComSTARNet Developer at ComSTAR, s.r.o. (May 2005 - May 2006)\n"
            "- Developed CRM systems using PHP and SQL."
        )
        self.section_body(
            "ComSTARNet Developer at ComSTAR, s.r.o. (May 2005 - May 2006)\n"
            "- Developed CRM systems using PHP and SQL."
        )
        self.section_body(
            "ComSTARNet Developer:\n"
            "- Developed CRM, maintained SQL modular systems."
        )
        self.section_body("ComSTARNet Developer - Designed scalable CRM systems with modular PHP/SQL integrations.\n")

        self.section_body("ComSTARNet Developer - Built CRM systems using PHP and SQL.\nControlled modular scalability.\n")
        self.section_body("Experience with modular system implementation remains clear.")
        self.section_body("ComSTAR Developer role fully formatted without nesting issues.\n")
        self.section_body(
            "ComSTARNet Developer:\nDesigned CRM systems using PHP and SQL. Ensured module-based scaling and team oversight."
        )
        self.section_body(
            "ComSTARNet Developer - Specialized in modular CRM development using PHP and SQL.\nEffectively maintained cross-functional workflows."
        )
        self.section_body("Streamlined modular CRM integrations for PHP-based systems, achieving scalability.\n")


        self.section_body("Fully resolved CRM modules with backward compatibility adjustments.\n")

        # Academic Career (condensed)
        self.section_title("Education")
        self.section_body(
            """Ph.D., Computer Science and Engineering (2009 - now)
Czech Technical University in Prague.
- Thesis Topic: Natural Language Text Compression.

M.S., Computer Science and Engineering (June 2008)
Czech Technical University in Prague.
- Thesis: Word-based Statistical Data Compression Methods."""
        )

        # Technical Skills
        self.section_title("Technical Skills")
        self.section_body(
            """Analysis and Design Techniques: Unified Process, UML, Use Cases, GRASP, and GoF Patterns.
Programming: C, C++, Java, PHP, UNIX shell scripting, SQL, and others.
Operating Systems: Linux, Windows, Solaris."""
        )

        # Footer
        self.ln(10)
        self.cell(0, 10, "Generated with ModernCV Generator", align="C")


# Generate the PDF
pdf = ModernCV()
pdf.generate_cv()
pdf.output("modern_cv.pdf")