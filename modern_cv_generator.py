from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY
from datetime import datetime
import os

class ModernCVGenerator:
    def __init__(self):
        self.personal_info = {
            'name': '<PERSON><PERSON> Pro<PERSON>ázka',
            'title': 'Senior IT Analyst & Systems Architect',
            'phone': '+420 775 687 323',
            'email': '<EMAIL>',
            'address': 'Nábřežní 52, 28561 Žleby, Czech Republic',
            'linkedin': 'linkedin.com/in/petr-procházka-197b2a14'
        }

        self.professional_summary = """
        Experienced Senior IT Analyst and Systems Architect with 15+ years of expertise in enterprise
        system design, CRM implementation, and facility management solutions. Proven track record
        delivering complex IT projects for major telecommunications and financial institutions
        including T-Mobile, Vodafone, and Komerční Banka. Strong background in system analysis,
        database design, and cross-functional team leadership.
        """

        self.key_achievements = [
            "Led enterprise system implementations for major Czech telecommunications providers (T-Mobile, Vodafone)",
            "Designed and delivered CRM and Facility Management solutions for financial sector (Komerční Banka)",
            "Successfully managed IT projects for aviation industry (ČSA) and logistics (ABF)",
            "Developed innovative data compression algorithms published in international conferences",
            "Awarded Dean's Prize for Excellence in Diploma Thesis (2008)"
        ]

        self.professional_experience = [
            {
                'company': 'Alstanet, s.r.o.',
                'position': 'Senior IT Analyst & Systems Architect',
                'period': 'May 2006 - Present',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Lead consultant for enterprise system analysis and design of Facility Management Systems and CRM solutions',
                    'Successfully delivered complex IT projects for major clients including T-Mobile, Vodafone, Komerční Banka, ČSA, and ABF',
                    'Designed scalable system architectures supporting thousands of concurrent users',
                    'Collaborated with cross-functional teams to ensure seamless system integration and deployment',
                    'Provided strategic IT consulting to optimize business processes and improve operational efficiency'
                ]
            },
            {
                'company': 'ComSTAR, s.r.o.',
                'position': 'CRM Systems Developer',
                'period': 'May 2005 - May 2006',
                'location': 'Prague, Czech Republic',
                'achievements': [
                    'Developed comprehensive CRM system using PHP and SQL technologies',
                    'Implemented database optimization strategies improving system performance by 40%',
                    'Created user-friendly interfaces enhancing customer relationship management processes'
                ]
            }
        ]

        self.technical_skills = {
            'Programming Languages': ['Java', 'C/C++', 'PHP', 'Python', 'SQL', 'JavaScript'],
            'Databases': ['MySQL', 'PostgreSQL', 'Oracle', 'SQL Server'],
            'Frameworks & Tools': ['Spring', 'Hibernate', 'Laravel', 'Git', 'Jenkins'],
            'System Design': ['UML', 'Enterprise Architect', 'MS Visio', 'CASE Studio'],
            'Methodologies': ['Unified Process', 'Agile', 'GRASP', 'GoF Design Patterns'],
            'Operating Systems': ['Linux', 'Windows Server', 'Unix variants', 'Solaris']
        }

        self.education = [
            {
                'degree': 'Ph.D. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2009 - 2014',
                'focus': 'Natural Language Processing & Data Compression'
            },
            {
                'degree': 'M.S. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2006 - 2008',
                'focus': 'System Programming'
            },
            {
                'degree': 'B.S. in Computer Science and Engineering',
                'institution': 'Czech Technical University in Prague',
                'period': '2003 - 2006',
                'focus': 'Computer Science'
            }
        ]

        self.certifications_awards = [
            'Dean\'s Prize for Excellent Diploma Thesis (2008)',
            'University Scholarship (2004-2008)',
            'Certificate of Achievement - Binghamton University, NY (2008)'
        ]

    def generate_html_template(self):
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{name} - CV</title>
    <style>
        {css_styles}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="header-content">
                <h1 class="name">{name}</h1>
                <h2 class="title">{title}</h2>
                <div class="contact-info">
                    <div class="contact-item">
                        <span class="icon">📞</span>
                        <span>{phone}</span>
                    </div>
                    <div class="contact-item">
                        <span class="icon">✉️</span>
                        <span>{email}</span>
                    </div>
                    <div class="contact-item">
                        <span class="icon">📍</span>
                        <span>{address}</span>
                    </div>
                    <div class="contact-item">
                        <span class="icon">💼</span>
                        <span>{linkedin}</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Professional Summary -->
        <section class="section">
            <h3 class="section-title">Professional Summary</h3>
            <p class="summary">{professional_summary}</p>
        </section>

        <!-- Key Achievements -->
        <section class="section">
            <h3 class="section-title">Key Achievements</h3>
            <ul class="achievements-list">
                {key_achievements}
            </ul>
        </section>

        <!-- Professional Experience -->
        <section class="section">
            <h3 class="section-title">Professional Experience</h3>
            {professional_experience}
        </section>

        <!-- Technical Skills -->
        <section class="section">
            <h3 class="section-title">Technical Skills</h3>
            <div class="skills-grid">
                {technical_skills}
            </div>
        </section>

        <!-- Education -->
        <section class="section">
            <h3 class="section-title">Education</h3>
            {education}
        </section>

        <!-- Certifications & Awards -->
        <section class="section">
            <h3 class="section-title">Certifications & Awards</h3>
            <ul class="awards-list">
                {certifications_awards}
            </ul>
        </section>
    </div>
</body>
</html>
        """

    def generate_css_styles(self):
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .name {
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }

        .title {
            font-size: 1.3em;
            font-weight: 400;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }

        .icon {
            font-size: 1.1em;
        }

        .section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-size: 1.4em;
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            font-weight: 600;
        }

        .summary {
            font-size: 1.05em;
            line-height: 1.7;
            text-align: justify;
            color: #555;
        }

        .achievements-list {
            list-style: none;
        }

        .achievements-list li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
            font-size: 1.02em;
            line-height: 1.6;
        }

        .achievements-list li:before {
            content: "▶";
            color: #667eea;
            position: absolute;
            left: 0;
            top: 8px;
        }

        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #f0f0f0;
        }

        .experience-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .job-header {
            margin-bottom: 15px;
        }

        .job-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .company {
            font-size: 1.1em;
            color: #667eea;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .job-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.95em;
            color: #666;
            margin-bottom: 15px;
        }

        .job-achievements {
            list-style: none;
        }

        .job-achievements li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
            line-height: 1.5;
        }

        .job-achievements li:before {
            content: "•";
            color: #667eea;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .skill-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .skill-category h4 {
            color: #333;
            margin-bottom: 12px;
            font-size: 1.1em;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .skill-tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .education-item {
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .degree {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .institution {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .education-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #666;
        }

        .awards-list {
            list-style: none;
        }

        .awards-list li {
            padding: 10px 0;
            padding-left: 25px;
            position: relative;
            font-size: 1.02em;
            line-height: 1.5;
        }

        .awards-list li:before {
            content: "🏆";
            position: absolute;
            left: 0;
            top: 10px;
        }

        @media print {
            body {
                background: white;
            }

            .container {
                box-shadow: none;
                max-width: none;
            }

            .section {
                page-break-inside: avoid;
            }
        }
        """

    def format_professional_experience(self):
        html = ""
        for exp in self.professional_experience:
            achievements_html = ""
            for achievement in exp['achievements']:
                achievements_html += f"<li>{achievement}</li>"

            html += f"""
            <div class="experience-item">
                <div class="job-header">
                    <div class="job-title">{exp['position']}</div>
                    <div class="company">{exp['company']}</div>
                    <div class="job-meta">
                        <span>{exp['period']}</span>
                        <span>{exp['location']}</span>
                    </div>
                </div>
                <ul class="job-achievements">
                    {achievements_html}
                </ul>
            </div>
            """
        return html

    def format_technical_skills(self):
        html = ""
        for category, skills in self.technical_skills.items():
            skills_html = ""
            for skill in skills:
                skills_html += f'<span class="skill-tag">{skill}</span>'

            html += f"""
            <div class="skill-category">
                <h4>{category}</h4>
                <div class="skill-tags">
                    {skills_html}
                </div>
            </div>
            """
        return html

    def format_education(self):
        html = ""
        for edu in self.education:
            html += f"""
            <div class="education-item">
                <div class="degree">{edu['degree']}</div>
                <div class="institution">{edu['institution']}</div>
                <div class="education-meta">
                    <span>{edu['period']}</span>
                    <span>Focus: {edu['focus']}</span>
                </div>
            </div>
            """
        return html

    def format_key_achievements(self):
        html = ""
        for achievement in self.key_achievements:
            html += f"<li>{achievement}</li>"
        return html

    def format_certifications_awards(self):
        html = ""
        for award in self.certifications_awards:
            html += f"<li>{award}</li>"
        return html

    def generate_cv(self, output_filename="petr_prochazka_modern_cv.pdf"):
        # Generate HTML content
        html_template = self.generate_html_template()
        css_styles = self.generate_css_styles()

        # Format sections
        professional_experience = self.format_professional_experience()
        technical_skills = self.format_technical_skills()
        education = self.format_education()
        key_achievements = self.format_key_achievements()
        certifications_awards = self.format_certifications_awards()

        # Fill in the template
        html_content = html_template.format(
            name=self.personal_info['name'],
            title=self.personal_info['title'],
            phone=self.personal_info['phone'],
            email=self.personal_info['email'],
            address=self.personal_info['address'],
            linkedin=self.personal_info['linkedin'],
            professional_summary=self.professional_summary.strip(),
            key_achievements=key_achievements,
            professional_experience=professional_experience,
            technical_skills=technical_skills,
            education=education,
            certifications_awards=certifications_awards,
            css_styles=css_styles
        )

        # Save HTML file for debugging
        with open("cv_debug.html", "w", encoding="utf-8") as f:
            f.write(html_content)

        # Generate PDF
        try:
            HTML(string=html_content).write_pdf(output_filename)
            print(f"✅ Modern CV successfully generated: {output_filename}")
            print(f"📄 Debug HTML file saved: cv_debug.html")
            return True
        except Exception as e:
            print(f"❌ Error generating PDF: {str(e)}")
            return False

if __name__ == "__main__":
    generator = ModernCVGenerator()
    generator.generate_cv()
