import os
import subprocess
import sys

def open_pdf(filename):
    """Open PDF file with default system viewer"""
    try:
        if os.path.exists(filename):
            if sys.platform.startswith('win'):
                os.startfile(filename)
            elif sys.platform.startswith('darwin'):  # macOS
                subprocess.call(['open', filename])
            else:  # Linux
                subprocess.call(['xdg-open', filename])
            print(f"✅ Opened: {filename}")
        else:
            print(f"❌ File not found: {filename}")
    except Exception as e:
        print(f"❌ Error opening {filename}: {str(e)}")

if __name__ == "__main__":
    print("🔍 Opening generated CV files...")
    print("=" * 50)

    # List of CV files to open
    cv_files = [
        "petr_prochazka_premium_cv.pdf",
        "petr_prochazka_final_cv.pdf"
    ]

    for cv_file in cv_files:
        open_pdf(cv_file)

    print("=" * 50)
    print("📄 All available CV files:")
    for file in os.listdir('.'):
        if file.endswith('.pdf'):
            print(f"  • {file}")
